import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateSessionLearnerActionTable1750100000000 implements MigrationInterface {
    name = 'CreateSessionLearnerActionTable1750100000000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Check if table already exists
        const hasTable = await queryRunner.hasTable("session_learner_action");
        
        if (!hasTable) {
            // Create the enum type for job_type
            await queryRunner.query(`
                DO $$ BEGIN
                    CREATE TYPE "session_learner_action_job_type_enum" AS ENUM('On-the-job', 'Off-the-job');
                EXCEPTION
                    WHEN duplicate_object THEN null;
                END $$;
            `);
            
            // Create the session_learner_action table
            await queryRunner.query(`
                CREATE TABLE "session_learner_action" (
                    "action_id" SERIAL NOT NULL,
                    "learner_plan_id" integer,
                    "learner_id" integer,
                    "action_name" character varying NOT NULL,
                    "action_description" text,
                    "target_date" TIMESTAMP NOT NULL,
                    "job_type" "session_learner_action_job_type_enum" NOT NULL DEFAULT 'On-the-job',
                    "added_by" integer,
                    "unit" json,
                    "file_attachment" json,
                    "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                    "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                    CONSTRAINT "PK_session_learner_action" PRIMARY KEY ("action_id")
                )
            `);
            
            // Add foreign key constraints
            await queryRunner.query(`
                ALTER TABLE "session_learner_action"
                ADD CONSTRAINT "FK_session_learner_action_learner_plan"
                FOREIGN KEY ("learner_plan_id") REFERENCES "learner_plan"("learner_plan_id") ON DELETE CASCADE ON UPDATE NO ACTION
            `);
            
            await queryRunner.query(`
                ALTER TABLE "session_learner_action" 
                ADD CONSTRAINT "FK_session_learner_action_learner" 
                FOREIGN KEY ("learner_id") REFERENCES "learner"("learner_id") ON DELETE CASCADE ON UPDATE NO ACTION
            `);
            
            await queryRunner.query(`
                ALTER TABLE "session_learner_action" 
                ADD CONSTRAINT "FK_session_learner_action_added_by" 
                FOREIGN KEY ("added_by") REFERENCES "users"("user_id") ON DELETE SET NULL ON UPDATE NO ACTION
            `);
            
            console.log('✅ Created session_learner_action table with foreign key constraints');
        } else {
            console.log('ℹ️ session_learner_action table already exists');
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop the table
        const hasTable = await queryRunner.hasTable("session_learner_action");
        
        if (hasTable) {
            await queryRunner.query(`DROP TABLE "session_learner_action"`);
            console.log('✅ Dropped session_learner_action table');
        }
        
        // Drop the enum type
        try {
            await queryRunner.query(`DROP TYPE IF EXISTS "session_learner_action_job_type_enum"`);
            console.log('✅ Dropped session_learner_action_job_type_enum type');
        } catch (error) {
            console.log('ℹ️ Could not remove enum type (might be in use by other tables)');
        }
    }
}
