# Course List API Documentation

## Simple Course List API

### **GET /api/v1/learner-plan/courses**

Get a simple list of courses with id, name, and code for a specific trainer and learner combination.

#### **Parameters**
- `trainer_id` (required): The ID of the trainer
- `learner_id` (required): The ID of the learner

#### **Example Request**
```bash
GET http://localhost:4000/api/v1/learner-plan/courses?trainer_id=2&learner_id=5
Headers:
- Authorization: Bearer YOUR_TOKEN
```

#### **Example Response**
```json
{
    "message": "Course list retrieved successfully",
    "status": true,
    "data": {
        "trainer_id": 2,
        "learner_id": 5,
        "total_courses": 2,
        "courses": [
            {
                "course_id": 1,
                "course_name": "Software Development Level 3",
                "course_code": "SD001"
            },
            {
                "course_id": 2,
                "course_name": "Digital Marketing Level 2",
                "course_code": "DM002"
            }
        ]
    }
}
```

#### **Error Responses**

**400 Bad Request - Missing trainer_id**
```json
{
    "message": "trainer_id is required",
    "status": false
}
```

**400 Bad Request - Missing learner_id**
```json
{
    "message": "learner_id is required",
    "status": false
}
```

**500 Internal Server Error**
```json
{
    "message": "Internal Server Error",
    "status": false,
    "error": "Error details"
}
```

## Features

- ✅ Simple and lightweight API
- ✅ Returns only essential course information (id, name, code)
- ✅ Automatically removes duplicate courses
- ✅ Validates required parameters
- ✅ Proper error handling
- ✅ TypeScript type safety

## Usage Notes

1. Both `trainer_id` and `learner_id` are required parameters
2. The API returns courses where the specified trainer and learner are connected in the UserCourse table
3. Duplicate courses are automatically filtered out
4. Only courses with valid course_id are returned
5. The API requires proper authentication (Bearer token)
