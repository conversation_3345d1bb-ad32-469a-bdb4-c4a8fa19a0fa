import { Router } from 'express';
import { SessionLearnerActionController } from '../controllers/SessionLearnerActionController';
import { verifyToken } from '../middleware/verifyToken';
import { upload } from '../util/multer';

const router = Router();
const sessionLearnerActionController = new SessionLearnerActionController();

// Create action with optional file upload
router.post('/create', verifyToken, upload.single('file'), sessionLearnerActionController.createAction);

// Get actions by session (with optional learner filter)
router.get('/session/:session_id', verifyToken, sessionLearnerActionController.getActionsBySession);

// Update action with optional file upload
router.patch('/update/:id', verifyToken, upload.single('file'), sessionLearnerActionController.updateAction);

// Delete action
router.delete('/delete/:id', verifyToken, sessionLearnerActionController.deleteAction);

// Get options for dropdowns
router.get('/options', verifyToken, sessionLearnerActionController.getActionOptions);

export default router;
