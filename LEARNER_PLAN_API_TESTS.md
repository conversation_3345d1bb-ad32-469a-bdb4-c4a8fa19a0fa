# LearnerPlan API Testing Guide

## 🎯 **Overview**
The LearnerPlan module is a complete copy of the Session module with the following enhancements:
- ✅ All session form input handling retained
- ✅ Course list filtering based on assessor and learner
- ✅ New `repeatSession` boolean field (default: false)
- ✅ Proper renaming from session to learnerPlan
- ✅ Notification functionality included
- ✅ No email functionality (as requested)

## 📋 **API Endpoints**

### **Base URL:** `/api/v1/learner-plan`

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/create` | Create new learner plan |
| PATCH | `/update/:id` | Update learner plan |
| DELETE | `/delete/:id` | Delete learner plan |
| GET | `/list` | Get learner plans with pagination |
| GET | `/get/:id` | Get single learner plan |
| GET | `/list/month` | Get learner plans by month |
| POST | `/test-notification` | Test notification functionality |

## 🧪 **Test Cases**

### **1. Create Learner Plan**
```bash
POST http://localhost:4000/api/v1/learner-plan/create
Headers:
- Authorization: Bearer YOUR_TOKEN
- Content-Type: application/json

Body:
{
    "assessor_id": 2,
    "learners": [19, 20],
    "courses": [1, 2],
    "title": "Advanced Assessment Plan",
    "description": "Comprehensive assessment plan for advanced learners",
    "location": "Assessment Room A",
    "startDate": "2024-01-25T10:00:00Z",
    "Duration": "120",
    "type": "FormalReview",
    "repeatSession": true
}
```

**Expected Response:**
```json
{
    "message": "Learner plan created successfully",
    "status": true,
    "data": {
        "learner_plan_id": 1,
        "title": "Advanced Assessment Plan",
        "assessor_details": {...},
        "learners": [...],
        "courses": [...],
        "filtered_course_list": [...],
        "repeatSession": true
    }
}
```

### **2. Get Learner Plans with Sorting**
```bash
GET http://localhost:4000/api/v1/learner-plan/list?sortBy=asc&page=1&limit=10
Headers:
- Authorization: Bearer YOUR_TOKEN
```

### **3. Get Filtered Learner Plans**
```bash
GET http://localhost:4000/api/v1/learner-plan/list?assessor_id=2&type=FormalReview&sortBy=desc
Headers:
- Authorization: Bearer YOUR_TOKEN
```

### **4. Update Learner Plan**
```bash
PATCH http://localhost:4000/api/v1/learner-plan/update/1
Headers:
- Authorization: Bearer YOUR_TOKEN
- Content-Type: application/json

Body:
{
    "title": "Updated Assessment Plan",
    "repeatSession": false,
    "Attended": "Attended"
}
```

### **5. Get Single Learner Plan with Course List**
```bash
GET http://localhost:4000/api/v1/learner-plan/get/1
Headers:
- Authorization: Bearer YOUR_TOKEN
```

### **6. Test Notification**
```bash
POST http://localhost:4000/api/v1/learner-plan/test-notification
Headers:
- Authorization: Bearer YOUR_TOKEN
- Content-Type: application/json

Body:
{
    "learner_id": 19
}
```

## 🔧 **Key Features**

### **1. Course List Filtering**
- Automatically filters courses based on assessor and learner relationships
- Returns courses where assessor is trainer, IQA, LIQA, or EQA
- Only shows courses assigned to the selected learners

### **2. Repeat Session Support**
- New boolean field `repeatSession` (default: false)
- Can be set during creation or updated later
- Included in all responses

### **3. Enhanced Response Data**
```json
{
    "data": {
        "learner_plan_id": 1,
        "title": "Assessment Plan",
        "assessor_details": {
            "user_id": 2,
            "user_name": "<EMAIL>",
            "email": "<EMAIL>"
        },
        "learners": [...],
        "courses": [...],
        "filtered_course_list": [
            {
                "user_course_id": 1,
                "course": {
                    "course_id": 1,
                    "course_name": "JavaScript Fundamentals",
                    "course_code": "JS101"
                },
                "learner": {
                    "learner_id": 19,
                    "first_name": "John",
                    "last_name": "Doe"
                }
            }
        ],
        "repeatSession": true
    }
}
```

### **4. Notification System**
- Sends WebSocket notifications to learners
- No email functionality (as requested)
- Test endpoint available for debugging

## 📊 **Database Schema**

### **LearnerPlan Entity:**
- `learner_plan_id` (Primary Key)
- `assessor_id` (Foreign Key to User)
- `learners` (Many-to-Many with Learner)
- `courses` (Many-to-Many with Course)
- `title`, `description`, `location`
- `startDate`, `Duration`
- `type` (Enum: LearnerPlanType)
- `Attended` (Enum: LearnerPlanAttendedStatus)
- `repeatSession` (Boolean, default: false)
- `created_at`, `updated_at`

### **Junction Tables:**
- `learner_plan_learners`
- `learner_plan_courses`

## ✅ **Verification Checklist**

- [ ] Create learner plan with all fields
- [ ] Verify course filtering works correctly
- [ ] Test repeatSession field functionality
- [ ] Check notification sending
- [ ] Verify sorting works (asc/desc)
- [ ] Test pagination
- [ ] Verify filtering by assessor/learner/type
- [ ] Test update functionality
- [ ] Test delete functionality
- [ ] Verify monthly view works

## 🚀 **Ready for Use**

The LearnerPlan module is fully functional and ready for production use with all requested features implemented!
