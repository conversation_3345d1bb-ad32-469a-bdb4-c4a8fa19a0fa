# Learner Plan Repeat Session API Examples

## 1. Get Dropdown Options for Form

```bash
GET http://localhost:4000/api/v1/learner-plan/repeat/options
Headers:
- Authorization: Bearer YOUR_TOKEN
```

**Response:**
```json
{
    "message": "Repeat session options fetched successfully",
    "status": true,
    "data": {
        "frequencies": ["Daily", "Weekly", "Monthly"],
        "file_types": ["ILP", "Review", "Assessment", "General"],
        "session_types": ["First Session", "Follow Up", "Review", "Assessment", "Final"]
    }
}
```

## 2. Create Learner Plan WITHOUT Repeat Session (Normal)

```bash
POST http://localhost:4000/api/v1/learner-plan/create
Headers:
- Authorization: Bearer YOUR_TOKEN
- Content-Type: application/json

Body:
{
    "assessor_id": 1,
    "learners": [1, 2],
    "courses": [1],
    "title": "Initial Assessment",
    "description": "First assessment session",
    "location": "Training Room A",
    "startDate": "2024-01-15T10:00:00Z",
    "Duration": "2 hours",
    "type": "InitialSession",
    "repeatSession": false
}
```

## 3. Create Learner Plan WITH Repeat Session (Weekly, Every 2 Weeks)

```bash
POST http://localhost:4000/api/v1/learner-plan/create
Headers:
- Authorization: Bearer YOUR_TOKEN
- Content-Type: application/json

Body:
{
    "assessor_id": 1,
    "learners": [1, 2, 3],
    "courses": [1, 2],
    "title": "Weekly Progress Review",
    "description": "Regular progress review sessions",
    "location": "Training Room B",
    "startDate": "2024-01-15T10:00:00Z",
    "Duration": "1.5 hours",
    "type": "FormalReview",
    "repeatSession": true,
    "repeat_frequency": "Weekly",
    "repeat_every": 2,
    "include_holidays": false,
    "include_weekends": false,
    "repeat_end_date": "2024-06-30",
    "upload_session_files": false
}
```

## 4. Create Learner Plan WITH File Attachments

```bash
POST http://localhost:4000/api/v1/learner-plan/create
Headers:
- Authorization: Bearer YOUR_TOKEN
- Content-Type: application/json

Body:
{
    "assessor_id": 1,
    "learners": [1, 2],
    "courses": [1],
    "title": "Monthly Assessment",
    "description": "Monthly formal assessment",
    "location": "Assessment Center",
    "startDate": "2024-01-15T14:00:00Z",
    "Duration": "3 hours",
    "type": "FormalReview",
    "repeatSession": true,
    "repeat_frequency": "Monthly",
    "repeat_every": 1,
    "include_holidays": true,
    "include_weekends": true,
    "repeat_end_date": "2024-12-31",
    "upload_session_files": true,
    "file_attachments": [
        {
            "file_type": "Assessment",
            "session_type": "Review",
            "file_url": "/uploads/assessment_checklist.pdf",
            "file_name": "Assessment_Checklist.pdf",
            "uploaded_at": "2024-01-15T10:00:00Z"
        }
    ]
}
```

## 5. Upload Additional Files to Existing Learner Plan

```bash
POST http://localhost:4000/api/v1/learner-plan/repeat/upload-files
Headers:
- Authorization: Bearer YOUR_TOKEN
- Content-Type: multipart/form-data

Form Data:
- file: [FILE_TO_UPLOAD]
- learner_plan_id: 1
- file_type: "ILP"
- session_type: "First Session"
```

## 6. Cancel Repeat Session (Keep Existing Sessions)

```bash
PATCH http://localhost:4000/api/v1/learner-plan/repeat/cancel/1
Headers:
- Authorization: Bearer YOUR_TOKEN
- Content-Type: application/json

Body:
{
    "cancel_future_sessions": false
}
```

## 7. Cancel Repeat Session (Cancel Future Sessions)

```bash
PATCH http://localhost:4000/api/v1/learner-plan/repeat/cancel/1
Headers:
- Authorization: Bearer YOUR_TOKEN
- Content-Type: application/json

Body:
{
    "cancel_future_sessions": true
}
```

## Frontend Integration Guide

### Step 1: Load Dropdown Options
```javascript
// Load options when form loads
const response = await fetch('/api/v1/learner-plan/repeat/options', {
    headers: { 'Authorization': `Bearer ${token}` }
});
const options = await response.json();
// Populate dropdowns with options.data.frequencies, file_types, session_types
```

### Step 2: Show/Hide Repeat Form
```javascript
// When "Repeat session" checkbox changes
const repeatCheckbox = document.getElementById('repeatSession');
const repeatForm = document.getElementById('repeatSessionForm');

repeatCheckbox.addEventListener('change', (e) => {
    if (e.target.checked) {
        repeatForm.style.display = 'block';
    } else {
        repeatForm.style.display = 'none';
    }
});
```

### Step 3: Handle File Upload Modal
```javascript
// When "Upload files" button is clicked
const uploadButton = document.getElementById('uploadFilesBtn');
uploadButton.addEventListener('click', () => {
    // Show modal with file type and session type dropdowns
    showFileUploadModal();
});

function showFileUploadModal() {
    // Modal contains:
    // - File input
    // - File type dropdown (populated from options)
    // - Session type dropdown (populated from options)
    // - Submit button that calls /repeat/upload-files
}
```

### Step 4: Submit Form
```javascript
// When form is submitted
const formData = {
    // Regular learner plan fields...
    repeatSession: document.getElementById('repeatSession').checked,
    
    // Only include repeat fields if repeatSession is true
    ...(document.getElementById('repeatSession').checked && {
        repeat_frequency: document.getElementById('frequency').value,
        repeat_every: parseInt(document.getElementById('repeatEvery').value),
        include_holidays: document.getElementById('includeHolidays').checked,
        include_weekends: document.getElementById('includeWeekends').checked,
        repeat_end_date: document.getElementById('endDate').value,
        upload_session_files: document.getElementById('uploadFiles').checked,
        file_attachments: uploadedFiles // Array of uploaded files
    })
};

// Submit to /api/v1/learner-plan/create
```

## Error Handling

All APIs return standard error responses:
```json
{
    "message": "Error description",
    "status": false,
    "error": "Detailed error message"
}
```

Common error scenarios:
- **400**: Missing required fields or invalid repeat configuration
- **404**: Learner plan not found (for file upload/cancel operations)
- **500**: Internal server error
