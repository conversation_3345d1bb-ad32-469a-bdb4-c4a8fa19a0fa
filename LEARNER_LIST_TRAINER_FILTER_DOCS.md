# Learner List API - Trainer Filter Enhancement

## Updated GET /api/v1/learner/list

The `getLearnerList` API has been enhanced to support filtering learners by trainer/assessor ID.

### **New Parameter**
- `trainer_id` (optional): Filter learners to show only those assigned to the specified trainer

### **Example Requests**

#### **Get all learners for a specific trainer**
```bash
GET http://localhost:4000/api/v1/learner/list?trainer_id=5&page=1&limit=10
Headers:
- Authorization: Bearer YOUR_TOKEN
```

#### **Combine trainer filter with other filters**
```bash
GET http://localhost:4000/api/v1/learner/list?trainer_id=5&employer_id=2&status=In%20Training&page=1&limit=10
Headers:
- Authorization: Bearer YOUR_TOKEN
```

#### **Search learners by keyword for a specific trainer**
```bash
GET http://localhost:4000/api/v1/learner/list?trainer_id=5&keyword=john&page=1&limit=10
Headers:
- Authorization: Bearer YOUR_TOKEN
```

### **Response Format**
The response format remains the same as the original API:

```json
{
    "message": "Learner fetched successfully",
    "status": true,
    "data": [
        {
            "learner_id": 1,
            "first_name": "John",
            "last_name": "Doe",
            "user_name": "john.doe",
            "email": "<EMAIL>",
            "mobile": "1234567890",
            "job_title": "Software Developer",
            "user_id": 123,
            "avatar": "https://example.com/avatar.jpg",
            "employer_id": 2,
            "employer_name": "Tech Company Ltd",
            "course": [
                {
                    "user_course_id": 1,
                    "course": {
                        "course_id": 1,
                        "course_name": "Software Development Level 3",
                        "course_code": "SD001"
                    },
                    "course_status": "In Training",
                    "start_date": "2024-01-15",
                    "end_date": "2024-12-15"
                }
            ]
        }
    ],
    "meta_data": {
        "page": 1,
        "items": 25,
        "page_size": 10,
        "pages": 3
    }
}
```

### **Existing Parameters (still supported)**
- `user_id` + `role`: Filter by user ID and role (EQA, IQA, LIQA, Employer, Trainer)
- `course_id`: Filter by course ID
- `employer_id`: Filter by employer ID
- `status`: Filter by course status (comma-separated)
- `keyword`: Search by email, username, first name, or last name
- `page`: Page number for pagination
- `limit`: Number of items per page
- `meta`: Include metadata in response (set to "true")

### **Implementation Details**

1. **Priority**: The `trainer_id` parameter takes precedence over the existing `user_id` + `role` combination
2. **Database Query**: Filters learners through the `UserCourse` table where `trainer_id` matches the provided value
3. **Compatibility**: Fully backward compatible with existing API usage
4. **Performance**: Uses efficient database joins to minimize query overhead

### **Use Cases**

- **Trainer Dashboard**: Show only learners assigned to the logged-in trainer
- **Assessor View**: Display learners for a specific assessor
- **Admin Panel**: Filter learners by trainer for administrative purposes
- **Reports**: Generate trainer-specific learner reports

### **Error Handling**

If no learners are found for the specified trainer, the API returns an empty array with appropriate metadata:

```json
{
    "message": "Learner fetched successfully",
    "status": true,
    "data": [],
    "meta_data": {
        "page": 1,
        "items": 0,
        "page_size": 10,
        "pages": 0
    }
}
```
