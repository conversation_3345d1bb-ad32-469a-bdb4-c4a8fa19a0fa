# Updated Learner Plan GET APIs with Repeat Session Fields

## Overview
The `getLearnerPlans`, `getLearnerPlan`, and `getLearnerPlansByMonth` APIs have been updated to include all repeat session fields and file attachments.

## API Endpoints

### 1. **Get All Learner Plans**
**GET** `/api/v1/learner-plan/list`

#### Query Parameters:
- `assessor_id` (optional): Filter by assessor ID
- `learners` (optional): Filter by learner IDs
- `type` (optional): Filter by session type
- `Attended` (optional): Filter by attendance status
- `sortBy` (optional): Sort order
- `page` (optional): Page number for pagination
- `limit` (optional): Items per page

#### Enhanced Response:
```json
{
    "message": "Learner plans fetched successfully",
    "status": true,
    "data": [
        {
            "learner_plan_id": 1,
            "title": "Weekly Progress Review",
            "description": "Regular progress review sessions",
            "location": "Training Room A",
            "startDate": "2024-01-15T10:00:00Z",
            "Duration": "2 hours",
            "type": "FormalReview",
            "Attended": null,
            
            // Repeat Session Fields
            "repeatSession": true,
            "repeat_frequency": "Weekly",
            "repeat_every": 2,
            "include_holidays": false,
            "include_weekends": false,
            "repeat_end_date": "2024-12-31T00:00:00Z",
            "upload_session_files": true,
            
            // File Attachments
            "file_attachments": [
                {
                    "file_type": "ILP",
                    "session_type": "First Session",
                    "session_scope": "first_session",
                    "file_name": "ILP_Template.pdf",
                    "file_size": 245760,
                    "file_url": "https://bucket.s3.amazonaws.com/LearnerPlan/1642345678_ILP_Template.pdf",
                    "s3_key": "LearnerPlan/1642345678_ILP_Template.pdf",
                    "uploaded_at": "2024-01-15T10:00:00Z"
                },
                {
                    "file_type": "Assessment",
                    "session_type": "Review",
                    "session_scope": "all_sessions",
                    "file_name": "Assessment_Checklist.pdf",
                    "file_size": 189440,
                    "file_url": "https://bucket.s3.amazonaws.com/LearnerPlan/1642345679_Assessment_Checklist.pdf",
                    "s3_key": "LearnerPlan/1642345679_Assessment_Checklist.pdf",
                    "uploaded_at": "2024-01-15T10:30:00Z"
                }
            ],
            
            // Timestamps
            "created_at": "2024-01-15T09:00:00Z",
            "updated_at": "2024-01-15T09:00:00Z",
            
            // Related Data
            "assessor_id": {
                "user_id": 5,
                "user_name": "john.trainer",
                "email": "<EMAIL>"
            },
            "learners": [
                {
                    "learner_id": 1,
                    "user_name": "jane.learner",
                    "email": "<EMAIL>"
                }
            ],
            "courses": [
                {
                    "course_id": 1,
                    "course_name": "Software Development Level 3",
                    "course_code": "SD001"
                }
            ]
        }
    ],
    "meta_data": {
        "page": 1,
        "items": 25,
        "page_size": 10,
        "pages": 3
    }
}
```

### 2. **Get Single Learner Plan**
**GET** `/api/v1/learner-plan/get/:id`

#### Enhanced Response:
```json
{
    "message": "Learner plan fetched successfully",
    "status": true,
    "data": {
        "learner_plan_id": 1,
        "title": "Weekly Progress Review",
        "description": "Regular progress review sessions",
        "location": "Training Room A",
        "startDate": "2024-01-15T10:00:00Z",
        "Duration": "2 hours",
        "type": "FormalReview",
        "Attended": null,
        
        // Complete Repeat Session Configuration
        "repeatSession": true,
        "repeat_frequency": "Weekly",
        "repeat_every": 2,
        "include_holidays": false,
        "include_weekends": false,
        "repeat_end_date": "2024-12-31T00:00:00Z",
        "upload_session_files": true,
        
        // All File Attachments with S3 URLs
        "file_attachments": [
            {
                "file_type": "ILP",
                "session_type": "First Session",
                "session_scope": "first_session",
                "file_name": "ILP_Template.pdf",
                "file_size": 245760,
                "file_url": "https://bucket.s3.amazonaws.com/LearnerPlan/1642345678_ILP_Template.pdf",
                "s3_key": "LearnerPlan/1642345678_ILP_Template.pdf",
                "uploaded_at": "2024-01-15T10:00:00Z"
            }
        ],
        
        // Timestamps
        "created_at": "2024-01-15T09:00:00Z",
        "updated_at": "2024-01-15T09:00:00Z",
        
        // Complete Related Data
        "assessor_id": {
            "user_id": 5,
            "user_name": "john.trainer",
            "email": "<EMAIL>"
        },
        "learners": [
            {
                "learner_id": 1,
                "user_name": "jane.learner",
                "email": "<EMAIL>"
            }
        ],
        "courses": [
            {
                "course_id": 1,
                "course_name": "Software Development Level 3",
                "course_code": "SD001"
            }
        ]
    }
}
```

### 3. **Get Learner Plans by Month**
**GET** `/api/v1/learner-plan/list/month`

#### Query Parameters:
- `year` (required): Year (e.g., 2024)
- `month` (required): Month (1-12)
- `learner_id` (optional): Filter by learner ID
- `assessor_id` (optional): Filter by assessor ID

#### Enhanced Response:
Returns the same enhanced structure as the list API, but filtered by the specified month and year.

## New Fields Included

### **Repeat Session Configuration**
```typescript
{
    repeatSession: boolean;
    repeat_frequency: 'Daily' | 'Weekly' | 'Monthly' | null;
    repeat_every: number | null;
    include_holidays: boolean;
    include_weekends: boolean;
    repeat_end_date: Date | null;
    upload_session_files: boolean;
}
```

### **File Attachments Array**
```typescript
{
    file_attachments: [
        {
            file_type: 'ILP' | 'Review' | 'Assessment' | 'General';
            session_type: 'First Session' | 'Follow Up' | 'Review' | 'Assessment' | 'Final';
            session_scope: 'first_session' | 'all_sessions';
            file_name: string;
            file_size: number;
            file_url: string;        // S3 public URL for direct access
            s3_key: string;         // S3 object key for file management
            uploaded_at: Date;
        }
    ]
}
```

### **Timestamps**
```typescript
{
    created_at: Date;
    updated_at: Date;
}
```

## Benefits

### ✅ **Complete Data**
- All repeat session configuration fields included
- File attachments with S3 URLs for direct access
- Timestamps for audit trail

### ✅ **Frontend Ready**
- Frontend can display repeat configuration
- Direct file access via S3 URLs
- All data needed for editing/updating

### ✅ **Consistent Structure**
- Same enhanced structure across all GET APIs
- Predictable response format
- Easy to consume in frontend applications

### ✅ **File Management**
- S3 URLs for immediate file access
- S3 keys for file deletion if needed
- File metadata (size, type, scope)

## Usage Examples

### **Check if Learner Plan has Repeat Configuration**
```javascript
if (learnerPlan.repeatSession) {
    console.log(`Repeats ${learnerPlan.repeat_frequency} every ${learnerPlan.repeat_every} until ${learnerPlan.repeat_end_date}`);
}
```

### **Access Uploaded Files**
```javascript
learnerPlan.file_attachments.forEach(file => {
    console.log(`File: ${file.file_name}`);
    console.log(`Access URL: ${file.file_url}`);
    console.log(`Scope: ${file.session_scope}`);
});
```

### **Filter Files by Scope**
```javascript
const firstSessionFiles = learnerPlan.file_attachments.filter(
    file => file.session_scope === 'first_session'
);

const allSessionFiles = learnerPlan.file_attachments.filter(
    file => file.session_scope === 'all_sessions'
);
```

All GET APIs now provide complete information about learner plans including repeat session configuration and file attachments with direct S3 access!
