# Updated Session Learner Action Implementation

## Overview
Successfully updated the Session Learner Action implementation based on requirements:
1. ✅ **Use Learner Plan ID** instead of Session ID
2. ✅ **Use S3 Upload** (handled by frontend) instead of multer
3. ✅ **Use authorizeRoles()** for authentication

## 🎯 **Updated Entity Structure**

### **SessionLearnerAction Entity**
```typescript
@Entity('session_learner_action')
export class SessionLearnerAction {
    action_id: number;
    learner_plan: LearnerPlan;    // ✅ Changed from Session to LearnerPlan
    learner: Learner;
    action_name: string;
    action_description: string;
    target_date: Date;
    job_type: JobType;            // On-the-job / Off-the-job
    added_by: User;               // Who added the action
    unit: object;                 // Optional unit selection
    file_attachment: {            // ✅ S3 file data (not multer)
        file_name: string;
        file_size: number;
        file_url: string;         // S3 URL
        s3_key: string;          // S3 key
        uploaded_at: Date;
    };
}
```

## 🚀 **Updated API Endpoints**

### **1. Create Action**
```bash
POST /api/v1/session-learner-action/create
Authorization: Bearer <token>
Content-Type: application/json

{
    "learner_plan_id": 1,        // ✅ Changed from session_id
    "learner_id": 2,
    "action_name": "Complete Module Assessment",
    "action_description": "Complete the assessment for Module 1",
    "target_date": "2024-01-20T10:00:00Z",
    "job_type": "On-the-job",
    "unit": {
        "unit_id": "U001",
        "unit_name": "Communication",
        "unit_ref": "COMM001"
    },
    "file_data": {               // ✅ S3 file data (not multer upload)
        "file_name": "assessment_guide.pdf",
        "file_size": 245760,
        "file_url": "https://bucket.s3.amazonaws.com/SessionActions/file.pdf",
        "s3_key": "SessionActions/1642345678_assessment_guide.pdf"
    }
}
```

**Response:**
```json
{
    "message": "Session learner action created successfully",
    "status": true,
    "data": {
        "action_id": 1,
        "action_name": "Complete Module Assessment",
        "target_date": "2024-01-20T10:00:00Z",
        "job_type": "On-the-job",
        "file_attachment": {
            "file_name": "assessment_guide.pdf",
            "file_url": "https://bucket.s3.amazonaws.com/SessionActions/file.pdf",
            "s3_key": "SessionActions/1642345678_assessment_guide.pdf"
        },
        "learner_plan": { "learner_plan_id": 1, "title": "Weekly Review" },
        "learner": { "learner_id": 2, "user_name": "jane.learner" },
        "added_by": { "user_id": 5, "user_name": "john.trainer" }
    }
}
```

### **2. Get Actions by Learner Plan**
```bash
GET /api/v1/session-learner-action/learner-plan/1?learner_id=2
Authorization: Bearer <token>
```

**Response:**
```json
{
    "message": "Learner plan actions fetched successfully",
    "status": true,
    "data": [
        {
            "action_id": 1,
            "action_name": "Complete Module Assessment",
            "target_date": "2024-01-20T10:00:00Z",
            "job_type": "On-the-job",
            "file_attachment": {
                "file_url": "https://bucket.s3.amazonaws.com/SessionActions/file.pdf"
            },
            "learner_plan": { "learner_plan_id": 1 },
            "learner": { "learner_id": 2, "user_name": "jane.learner" },
            "added_by": { "user_id": 5, "user_name": "john.trainer" }
        }
    ]
}
```

### **3. Update Action**
```bash
PATCH /api/v1/session-learner-action/update/1
Authorization: Bearer <token>
Content-Type: application/json

{
    "action_name": "Updated Action Name",
    "target_date": "2024-01-25T10:00:00Z",
    "file_data": {               // ✅ New S3 file data
        "file_name": "updated_file.pdf",
        "file_size": 189440,
        "file_url": "https://bucket.s3.amazonaws.com/SessionActions/new_file.pdf",
        "s3_key": "SessionActions/1642345680_updated_file.pdf"
    }
}
```

### **4. Delete Action**
```bash
DELETE /api/v1/session-learner-action/delete/1
Authorization: Bearer <token>
```

### **5. Get Options**
```bash
GET /api/v1/session-learner-action/options
Authorization: Bearer <token>
```

## 🗄️ **Database Changes**

### **Updated Table Structure**
```sql
-- Updated session_learner_action table
CREATE TABLE "session_learner_action" (
    "action_id" SERIAL PRIMARY KEY,
    "learner_plan_id" integer REFERENCES "learner_plan"("learner_plan_id") ON DELETE CASCADE,  -- ✅ Changed
    "learner_id" integer REFERENCES "learner"("learner_id") ON DELETE CASCADE,
    "action_name" character varying NOT NULL,
    "action_description" text,
    "target_date" TIMESTAMP NOT NULL,
    "job_type" session_learner_action_job_type_enum DEFAULT 'On-the-job',
    "added_by" integer REFERENCES "users"("user_id") ON DELETE SET NULL,
    "unit" json,
    "file_attachment" json,
    "created_at" TIMESTAMP DEFAULT now(),
    "updated_at" TIMESTAMP DEFAULT now()
);
```

### **Migration Applied**
```
✅ Updated session_learner_action table to use learner_plan_id instead of session_id
Migration UpdateSessionLearnerActionToUseLearnerPlan1750200000000 has been executed successfully.
```

## 🔐 **Authentication**

### **All endpoints use authorizeRoles()**
```typescript
// ✅ Updated router with authorizeRoles()
router.post('/create', authorizeRoles(), sessionLearnerActionController.createAction);
router.get('/learner-plan/:learner_plan_id', authorizeRoles(), sessionLearnerActionController.getActionsByLearnerPlan);
router.patch('/update/:id', authorizeRoles(), sessionLearnerActionController.updateAction);
router.delete('/delete/:id', authorizeRoles(), sessionLearnerActionController.deleteAction);
router.get('/options', authorizeRoles(), sessionLearnerActionController.getActionOptions);
```

## 📁 **File Upload Workflow**

### **S3 Upload Process (Frontend Handled)**
1. **Frontend uploads file to S3** using existing S3 upload APIs
2. **Frontend receives S3 response** with file_url and s3_key
3. **Frontend sends file data** in request body to create/update action
4. **Backend stores S3 file data** in file_attachment field

### **Example Frontend Implementation**
```javascript
// Step 1: Upload file to S3 (using existing API)
const fileFormData = new FormData();
fileFormData.append('file', fileInput.files[0]);

const s3Response = await fetch('/api/v1/upload/file', {
    method: 'POST',
    headers: { 'Authorization': `Bearer ${token}` },
    body: fileFormData
});

const s3Data = await s3Response.json();

// Step 2: Create action with S3 file data
const actionData = {
    learner_plan_id: 1,
    learner_id: 2,
    action_name: "Complete Assessment",
    file_data: {
        file_name: s3Data.data.file_name,
        file_size: s3Data.data.file_size,
        file_url: s3Data.data.file_url,
        s3_key: s3Data.data.s3_key
    }
};

const actionResponse = await fetch('/api/v1/session-learner-action/create', {
    method: 'POST',
    headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
    },
    body: JSON.stringify(actionData)
});
```

## ✅ **Key Changes Summary**

### **1. Entity Changes**
- ✅ **Changed**: `session: Session` → `learner_plan: LearnerPlan`
- ✅ **Updated**: Foreign key from `session_id` to `learner_plan_id`

### **2. Controller Changes**
- ✅ **Changed**: `session_id` parameter → `learner_plan_id`
- ✅ **Updated**: Method name `getActionsBySession` → `getActionsByLearnerPlan`
- ✅ **Removed**: Multer file handling
- ✅ **Added**: S3 file data parsing from request body

### **3. Router Changes**
- ✅ **Changed**: `/session/:session_id` → `/learner-plan/:learner_plan_id`
- ✅ **Removed**: `upload.single('file')` middleware
- ✅ **Updated**: `verifyToken` → `authorizeRoles()`

### **4. Database Changes**
- ✅ **Migration**: Updated table structure
- ✅ **Foreign Key**: Points to learner_plan table instead of session table

## 🚀 **Usage Examples**

### **Create Action with S3 File**
```javascript
// Frontend handles S3 upload first, then creates action
const createActionWithFile = async (learnerPlanId, learnerId, actionData, file) => {
    // Upload to S3
    const s3Data = await uploadFileToS3(file);
    
    // Create action with S3 data
    const response = await fetch('/api/v1/session-learner-action/create', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            learner_plan_id: learnerPlanId,
            learner_id: learnerId,
            ...actionData,
            file_data: s3Data
        })
    });
    
    return response.json();
};
```

### **Get Actions for Learner Plan**
```javascript
const getActionsForLearnerPlan = async (learnerPlanId, learnerId = null) => {
    const url = `/api/v1/session-learner-action/learner-plan/${learnerPlanId}${learnerId ? `?learner_id=${learnerId}` : ''}`;
    
    const response = await fetch(url, {
        headers: { 'Authorization': `Bearer ${token}` }
    });
    
    return response.json();
};
```

All changes have been successfully implemented and the system now uses learner_plan_id, S3 file handling, and authorizeRoles() authentication! 🎯
