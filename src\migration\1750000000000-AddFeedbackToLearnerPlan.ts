import { MigrationInterface, QueryRunner } from "typeorm";

export class AddFeedbackToLearnerPlan1750000000000 implements MigrationInterface {
    name = 'AddFeedbackToLearnerPlan1750000000000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Check if feedback column already exists
        const hasFeedbackColumn = await queryRunner.hasColumn("learner_plan", "feedback");
        
        if (!hasFeedbackColumn) {
            // Create the enum type first
            await queryRunner.query(`
                DO $$ BEGIN
                    CREATE TYPE "learner_plan_feedback_enum" AS ENUM('Good', 'Neutral', 'Bad');
                EXCEPTION
                    WHEN duplicate_object THEN null;
                END $$;
            `);
            
            // Add the feedback column with the enum type
            await queryRunner.query(`
                ALTER TABLE "learner_plan" 
                ADD "feedback" "learner_plan_feedback_enum"
            `);
            
            console.log('✅ Added feedback column to learner_plan table');
        } else {
            console.log('ℹ️ Feedback column already exists in learner_plan table');
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Remove the feedback column
        const hasFeedbackColumn = await queryRunner.hasColumn("learner_plan", "feedback");
        
        if (hasFeedbackColumn) {
            await queryRunner.query(`ALTER TABLE "learner_plan" DROP COLUMN "feedback"`);
            console.log('✅ Removed feedback column from learner_plan table');
        }
        
        // Drop the enum type (only if no other tables are using it)
        try {
            await queryRunner.query(`DROP TYPE IF EXISTS "learner_plan_feedback_enum"`);
            console.log('✅ Removed learner_plan_feedback_enum type');
        } catch (error) {
            console.log('ℹ️ Could not remove enum type (might be in use by other tables)');
        }
    }
}
