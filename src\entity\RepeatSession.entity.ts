import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { Session } from './Session.entity';

export enum RepeatFrequency {
    Daily = 'Daily',
    Weekly = 'Weekly',
    Monthly = 'Monthly'
}

export enum FileType {
    ILP = 'ILP',
    Review = 'Review',
    Assessment = 'Assessment',
    General = 'General'
}

export enum SessionFileType {
    FirstSession = 'First Session',
    FollowUp = 'Follow Up',
    Review = 'Review',
    Assessment = 'Assessment',
    Final = 'Final'
}

@Entity('repeat_session')
export class RepeatSession {
    @PrimaryGeneratedColumn()
    repeat_session_id: number;

    @ManyToOne(() => Session)
    @JoinColumn({ name: 'original_session_id', referencedColumnName: 'session_id' })
    original_session: Session;

    @Column({
        type: 'enum',
        enum: RepeatFrequency,
        default: RepeatFrequency.Weekly
    })
    frequency: RepeatFrequency;

    @Column({ type: 'integer', default: 1 })
    repeat_every: number; // e.g., every 2 weeks

    @Column({ type: 'boolean', default: false })
    include_holidays: boolean;

    @Column({ type: 'boolean', default: false })
    include_weekends: boolean;

    @Column({ type: 'timestamp' })
    end_date: Date;

    @Column({ type: 'boolean', default: false })
    upload_session_files: boolean;

    @Column({ type: 'json', nullable: true })
    file_attachments: {
        file_type: FileType;
        session_type: SessionFileType;
        file_url: string;
        file_name: string;
        uploaded_at: Date;
    }[];

    @Column({ type: 'json', nullable: true })
    generated_sessions: {
        session_id: number;
        scheduled_date: Date;
        status: 'pending' | 'created' | 'cancelled';
    }[];

    @Column({ type: 'boolean', default: true })
    is_active: boolean;

    @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    created_at: Date;

    @UpdateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP', onUpdate: 'CURRENT_TIMESTAMP' })
    updated_at: Date;
}
