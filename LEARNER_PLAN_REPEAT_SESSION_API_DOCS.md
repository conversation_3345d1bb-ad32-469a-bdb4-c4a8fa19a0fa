# Learner Plan Repeat Session API Documentation

## Overview
The Learner Plan module now supports repeat session functionality integrated directly into the existing learner plan structure. When the "Repeat session" checkbox is selected, additional form fields become available for configuring recurring sessions.

## Enhanced Entity Structure

### LearnerPlan Entity - New Fields
```typescript
{
    // Existing fields...
    repeatSession: boolean;
    
    // New Repeat Session Fields
    repeat_frequency: 'Daily' | 'Weekly' | 'Monthly';
    repeat_every: number;
    include_holidays: boolean;
    include_weekends: boolean;
    repeat_end_date: Date;
    upload_session_files: boolean;
    file_attachments: FileAttachment[];
    generated_sessions: GeneratedSession[];
}
```

## API Endpoints

### 1. **Create Learner Plan with Repeat Session**
**POST** `/api/v1/learner-plan/create`

Enhanced to support repeat session configuration.

#### Request Body:
```json
{
    "assessor_id": 1,
    "learners": [1, 2, 3],
    "courses": [1, 2],
    "title": "Weekly Assessment Session",
    "description": "Regular assessment sessions",
    "location": "Training Room A",
    "startDate": "2024-01-15T10:00:00Z",
    "Duration": "2 hours",
    "type": "FormalReview",
    "Attended": null,
    "repeatSession": true,
    
    // Repeat Session Configuration
    "repeat_frequency": "Weekly",
    "repeat_every": 2,
    "include_holidays": false,
    "include_weekends": false,
    "repeat_end_date": "2024-12-31",
    "upload_session_files": true,
    "file_attachments": [
        {
            "file_type": "ILP",
            "session_type": "First Session",
            "file_url": "/uploads/session_plan.pdf",
            "file_name": "Session_Plan.pdf",
            "uploaded_at": "2024-01-15T10:00:00Z"
        }
    ]
}
```

#### Response:
```json
{
    "message": "Learner plan created successfully",
    "status": true,
    "data": {
        "learner_plan_id": 1,
        "title": "Weekly Assessment Session",
        "repeatSession": true,
        "repeat_frequency": "Weekly",
        "repeat_every": 2,
        "generated_sessions": [
            {
                "learner_plan_id": 2,
                "scheduled_date": "2024-01-29T10:00:00Z",
                "status": "created",
                "title": "Weekly Assessment Session (Repeat)"
            }
        ],
        "assessor_details": {...},
        "learners": [...],
        "courses": [...]
    }
}
```

### 2. **Upload Session Files**
**POST** `/api/v1/learner-plan/repeat/upload-files`

Upload files for repeat session configuration.

#### Form Data:
- `file`: File to upload
- `learner_plan_id`: ID of the learner plan
- `file_type`: Type of file (ILP, Review, Assessment, General)
- `session_type`: Session type (First Session, Follow Up, Review, Assessment, Final)

#### Response:
```json
{
    "message": "File uploaded successfully",
    "status": true,
    "data": {
        "learner_plan": {...},
        "uploaded_file": {
            "file_type": "Assessment",
            "session_type": "Review",
            "file_url": "/uploads/assessment_template.pdf",
            "file_name": "Assessment_Template.pdf",
            "uploaded_at": "2024-01-15T10:30:00Z"
        }
    }
}
```

### 3. **Get Repeat Session Options**
**GET** `/api/v1/learner-plan/repeat/options`

Returns available options for dropdown fields.

#### Response:
```json
{
    "message": "Repeat session options fetched successfully",
    "status": true,
    "data": {
        "frequencies": ["Daily", "Weekly", "Monthly"],
        "file_types": ["ILP", "Review", "Assessment", "General"],
        "session_types": ["First Session", "Follow Up", "Review", "Assessment", "Final"]
    }
}
```

### 4. **Cancel Repeat Session**
**PATCH** `/api/v1/learner-plan/repeat/cancel/:id`

Cancel repeat session configuration and optionally cancel future sessions.

#### Request Body:
```json
{
    "cancel_future_sessions": true
}
```

#### Response:
```json
{
    "message": "Repeat learner plan cancelled successfully",
    "status": true,
    "data": {
        "cancelled_learner_plan": {...},
        "cancelled_future_sessions": true
    }
}
```

## Form Field Mapping

### Frontend Form Fields → API Parameters:

1. **"Repeat session" checkbox** → `repeatSession: boolean`
2. **Repeat frequency dropdown** → `repeat_frequency: "Daily" | "Weekly" | "Monthly"`
3. **Repeat every (x) week(s) input** → `repeat_every: number`
4. **Include holidays checkbox** → `include_holidays: boolean`
5. **Include weekends checkbox** → `include_weekends: boolean`
6. **End date picker** → `repeat_end_date: Date`
7. **Upload session file attachment checkbox** → `upload_session_files: boolean`
8. **Upload files button** → Opens modal for `/repeat/upload-files` endpoint

## Features

✅ **Integrated Design**: No separate entity needed - all fields in LearnerPlan
✅ **Conditional Fields**: Repeat fields only saved when `repeatSession` is true
✅ **Automatic Generation**: Creates future learner plans automatically
✅ **Smart Scheduling**: Respects weekend/holiday preferences
✅ **File Management**: Upload and categorize session files
✅ **Notification System**: Sends notifications for each generated session
✅ **Cancellation Support**: Cancel repeat configuration and future sessions
✅ **Status Tracking**: Track status of all generated sessions

## Workflow

1. User creates a learner plan normally
2. User checks "Repeat session" checkbox
3. Additional form fields appear
4. User configures repeat settings and uploads files
5. Submit to `/create` endpoint with all data
6. System automatically generates future learner plans
7. Notifications sent for each generated session
8. User can manage repeat sessions via cancel API
