# Session Learner Action Implementation

## Overview
Successfully implemented three key features:
1. **Session Learner Action APIs** - Manage tasks assigned to learners during sessions
2. **Attended Status Filter** - Filter learner plans by attendance status
3. **Multiple Learner Session Creation** - Enhanced session creation with validation

## 🎯 **Feature 1: Session Learner Action APIs**

### **Entity Structure**
```typescript
@Entity('session_learner_action')
export class SessionLearnerAction {
    action_id: number;
    session: Session;           // Link to session
    learner: <PERSON>rner;          // Link to learner
    action_name: string;       // Action name
    action_description: string; // Action description
    target_date: Date;         // Target completion date
    job_type: JobType;         // On-the-job / Off-the-job
    added_by: User;            // Who added the action (e.g., Assessor)
    unit: {                    // Optional unit selection
        unit_id: string;
        unit_name: string;
        unit_ref: string;
    };
    file_attachment: {         // Optional file upload
        file_name: string;
        file_size: number;
        file_url: string;      // S3 URL
        s3_key: string;        // S3 key
        uploaded_at: Date;
    };
}
```

### **API Endpoints**

#### **1. Create Action**
```bash
POST /api/v1/session-learner-action/create
Content-Type: multipart/form-data

Form Data:
- session_id: 1
- learner_id: 2
- action_name: "Complete Module Assessment"
- action_description: "Complete the assessment for Module 1"
- target_date: "2024-01-20T10:00:00Z"
- job_type: "On-the-job"
- unit: {"unit_id": "U001", "unit_name": "Communication", "unit_ref": "COMM001"}
- file: [FILE] (optional)
```

**Response:**
```json
{
    "message": "Session learner action created successfully",
    "status": true,
    "data": {
        "action_id": 1,
        "action_name": "Complete Module Assessment",
        "action_description": "Complete the assessment for Module 1",
        "target_date": "2024-01-20T10:00:00Z",
        "job_type": "On-the-job",
        "unit": {
            "unit_id": "U001",
            "unit_name": "Communication",
            "unit_ref": "COMM001"
        },
        "file_attachment": {
            "file_name": "assessment_guide.pdf",
            "file_size": 245760,
            "file_url": "https://bucket.s3.amazonaws.com/SessionActions/1642345678_assessment_guide.pdf",
            "s3_key": "SessionActions/1642345678_assessment_guide.pdf",
            "uploaded_at": "2024-01-15T10:00:00Z"
        },
        "session": { "session_id": 1, "title": "Initial Session" },
        "learner": { "learner_id": 2, "user_name": "jane.learner" },
        "added_by": { "user_id": 5, "user_name": "john.trainer" }
    }
}
```

#### **2. Get Actions by Session**
```bash
GET /api/v1/session-learner-action/session/1?learner_id=2
```

**Response:**
```json
{
    "message": "Session learner actions fetched successfully",
    "status": true,
    "data": [
        {
            "action_id": 1,
            "action_name": "Complete Module Assessment",
            "target_date": "2024-01-20T10:00:00Z",
            "job_type": "On-the-job",
            "file_attachment": {
                "file_url": "https://bucket.s3.amazonaws.com/SessionActions/file.pdf"
            },
            "session": { "session_id": 1 },
            "learner": { "learner_id": 2, "user_name": "jane.learner" },
            "added_by": { "user_id": 5, "user_name": "john.trainer" }
        }
    ]
}
```

#### **3. Update Action**
```bash
PATCH /api/v1/session-learner-action/update/1
Content-Type: multipart/form-data

Form Data:
- action_name: "Updated Action Name"
- target_date: "2024-01-25T10:00:00Z"
- file: [NEW_FILE] (optional)
```

#### **4. Delete Action**
```bash
DELETE /api/v1/session-learner-action/delete/1
```

#### **5. Get Options**
```bash
GET /api/v1/session-learner-action/options
```

**Response:**
```json
{
    "message": "Action options fetched successfully",
    "status": true,
    "data": {
        "job_types": ["On-the-job", "Off-the-job"],
        "units": [
            {
                "unit_id": "U001",
                "unit_name": "Communication Skills",
                "unit_ref": "COMM001",
                "course_name": "Software Development Level 3"
            }
        ]
    }
}
```

## 🎯 **Feature 2: Attended Status Filter**

### **Enhanced Learner Plan Listing**
```bash
GET /api/v1/learner-plan/list?Attended=Attended
GET /api/v1/learner-plan/list?Attended=Not%20Attended
GET /api/v1/learner-plan/list?Attended=Cancelled
```

**Filter Options:**
- `Attended` - Show only attended sessions
- `Not Attended` - Show only non-attended sessions  
- `Cancelled` - Show only cancelled sessions
- No filter - Show all sessions

## 🎯 **Feature 3: Multiple Learner Session Creation**

### **Enhanced Session Creation**
```bash
POST /api/v1/session/create
{
    "trainer_id": 5,
    "learners": [1, 2, 3],  // Multiple learners array
    "title": "Group Training Session",
    "description": "Training for multiple learners",
    "location": "Training Room A",
    "startDate": "2024-01-15T10:00:00Z",
    "Duration": "2 hours",
    "type": "InitialSession"
}
```

**Validation Added:**
- Ensures `trainer_id` is provided
- Validates `learners` is an array with at least one learner
- Proper error messages for missing required fields

**Response:**
```json
{
    "message": "session created successfully",
    "status": true,
    "data": {
        "session_id": 1,
        "trainer_id": { "user_id": 5, "user_name": "john.trainer" },
        "learners": [
            { "learner_id": 1, "user_name": "jane.learner" },
            { "learner_id": 2, "user_name": "bob.learner" },
            { "learner_id": 3, "user_name": "alice.learner" }
        ],
        "title": "Group Training Session"
    }
}
```

## 🗄️ **Database Changes**

### **New Table: session_learner_action**
```sql
CREATE TABLE "session_learner_action" (
    "action_id" SERIAL PRIMARY KEY,
    "session_id" integer REFERENCES "session"("session_id") ON DELETE CASCADE,
    "learner_id" integer REFERENCES "learner"("learner_id") ON DELETE CASCADE,
    "action_name" character varying NOT NULL,
    "action_description" text,
    "target_date" TIMESTAMP NOT NULL,
    "job_type" session_learner_action_job_type_enum DEFAULT 'On-the-job',
    "added_by" integer REFERENCES "users"("user_id") ON DELETE SET NULL,
    "unit" json,
    "file_attachment" json,
    "created_at" TIMESTAMP DEFAULT now(),
    "updated_at" TIMESTAMP DEFAULT now()
);
```

### **Enum Type**
```sql
CREATE TYPE "session_learner_action_job_type_enum" AS ENUM('On-the-job', 'Off-the-job');
```

## 📁 **Files Created/Modified**

### **New Files:**
- ✅ `src/entity/SessionLearnerAction.entity.ts`
- ✅ `src/controllers/SessionLearnerActionController.ts`
- ✅ `src/Routes/SessionLearnerActionRouter.ts`
- ✅ `src/migration/1750100000000-CreateSessionLearnerActionTable.ts`

### **Modified Files:**
- ✅ `src/controllers/SessionController.ts` - Added validation for multiple learners
- ✅ `src/Routes/index.ts` - Added new router
- ✅ `src/controllers/LearnerPlanController.ts` - Attended filter already implemented

## 🚀 **Usage Examples**

### **Frontend Integration**
```javascript
// Create action with file
const formData = new FormData();
formData.append('session_id', '1');
formData.append('learner_id', '2');
formData.append('action_name', 'Complete Assessment');
formData.append('target_date', '2024-01-20T10:00:00Z');
formData.append('job_type', 'On-the-job');
formData.append('file', fileInput.files[0]);

const response = await fetch('/api/v1/session-learner-action/create', {
    method: 'POST',
    headers: { 'Authorization': `Bearer ${token}` },
    body: formData
});

// Filter learner plans by attendance
const attendedSessions = await fetch('/api/v1/learner-plan/list?Attended=Attended');

// Create session with multiple learners
const sessionData = {
    trainer_id: 5,
    learners: [1, 2, 3],
    title: "Group Session"
};
const session = await fetch('/api/v1/session/create', {
    method: 'POST',
    headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
    },
    body: JSON.stringify(sessionData)
});
```

## ✅ **Migration Status**
- **Migration File**: `1750100000000-CreateSessionLearnerActionTable.ts`
- **Status**: ✅ **EXECUTED SUCCESSFULLY**
- **Table Created**: `session_learner_action`
- **Foreign Keys**: Session, Learner, User (added_by)
- **Enum Type**: `session_learner_action_job_type_enum`

All three features are now fully implemented and ready for use! 🎉
