import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { LearnerPlan } from './LearnerPlan.entity';
import { Learner } from './Learner.entity';
import { User } from './User.entity';

export enum JobType {
    OnTheJob = 'On-the-job',
    OffTheJob = 'Off-the-job'
}

@Entity('session_learner_action')
export class SessionLearnerAction {
    @PrimaryGeneratedColumn()
    action_id: number;

    @ManyToOne(() => LearnerPlan)
    @JoinColumn({ name: 'learner_plan_id', referencedColumnName: 'learner_plan_id' })
    learner_plan: LearnerPlan;

    @ManyToOne(() => Learner)
    @JoinColumn({ name: 'learner_id', referencedColumnName: 'learner_id' })
    learner: Learner;

    @Column({ type: 'varchar' })
    action_name: string;

    @Column({ type: 'text', nullable: true })
    action_description: string;

    @Column({ type: 'timestamp' })
    target_date: Date;

    @Column({
        type: 'enum',
        enum: JobType,
        default: JobType.OnTheJob
    })
    job_type: JobType;

    @ManyToOne(() => User)
    @JoinColumn({ name: 'added_by', referencedColumnName: 'user_id' })
    added_by: User;

    @Column({ type: 'json', nullable: true })
    unit: {
        unit_id: string;
        unit_name: string;
        unit_ref: string;
    };

    @Column({ type: 'json', nullable: true })
    file_attachment: {
        file_name: string;
        file_size: number;
        file_url: string;
        s3_key: string;
        uploaded_at: Date;
    };

    @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
    created_at: Date;

    @UpdateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP', onUpdate: 'CURRENT_TIMESTAMP' })
    updated_at: Date;
}
