import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateSessionLearnerActionFields1750400000000 implements MigrationInterface {
    name = 'UpdateSessionLearnerActionFields1750400000000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Check if table exists
        const hasTable = await queryRunner.hasTable("session_learner_action");
        
        if (hasTable) {
            // Remove learner_id column if it exists
            const hasLearnerIdColumn = await queryRunner.hasColumn("session_learner_action", "learner_id");
            if (hasLearnerIdColumn) {
                // Drop foreign key constraint first
                try {
                    await queryRunner.query(`
                        ALTER TABLE "session_learner_action" 
                        DROP CONSTRAINT IF EXISTS "FK_session_learner_action_learner"
                    `);
                } catch (error) {
                    console.log('ℹ️ Learner foreign key constraint not found or already dropped');
                }
                
                await queryRunner.query(`
                    ALTER TABLE "session_learner_action" 
                    DROP COLUMN "learner_id"
                `);
                console.log('✅ Removed learner_id column from session_learner_action table');
            }

            // Make job_type nullable
            const hasJobTypeColumn = await queryRunner.hasColumn("session_learner_action", "job_type");
            if (hasJobTypeColumn) {
                await queryRunner.query(`
                    ALTER TABLE "session_learner_action" 
                    ALTER COLUMN "job_type" DROP NOT NULL
                `);
                console.log('✅ Made job_type column nullable in session_learner_action table');
            }

            // Add trainer_feedback column
            const hasTrainerFeedbackColumn = await queryRunner.hasColumn("session_learner_action", "trainer_feedback");
            if (!hasTrainerFeedbackColumn) {
                await queryRunner.query(`
                    ALTER TABLE "session_learner_action" 
                    ADD "trainer_feedback" character varying
                `);
                console.log('✅ Added trainer_feedback column to session_learner_action table');
            }

            // Add learner_feedback column
            const hasLearnerFeedbackColumn = await queryRunner.hasColumn("session_learner_action", "learner_feedback");
            if (!hasLearnerFeedbackColumn) {
                await queryRunner.query(`
                    ALTER TABLE "session_learner_action" 
                    ADD "learner_feedback" character varying
                `);
                console.log('✅ Added learner_feedback column to session_learner_action table');
            }

            // Add time_spent column
            const hasTimeSpentColumn = await queryRunner.hasColumn("session_learner_action", "time_spent");
            if (!hasTimeSpentColumn) {
                await queryRunner.query(`
                    ALTER TABLE "session_learner_action" 
                    ADD "time_spent" integer
                `);
                console.log('✅ Added time_spent column to session_learner_action table');
            }

            // Add status column
            const hasStatusColumn = await queryRunner.hasColumn("session_learner_action", "status");
            if (!hasStatusColumn) {
                await queryRunner.query(`
                    ALTER TABLE "session_learner_action" 
                    ADD "status" boolean DEFAULT false
                `);
                console.log('✅ Added status column to session_learner_action table');
            }

        } else {
            console.log('ℹ️ session_learner_action table does not exist');
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Check if table exists
        const hasTable = await queryRunner.hasTable("session_learner_action");
        
        if (hasTable) {
            // Remove new columns
            const columns = ['trainer_feedback', 'learner_feedback', 'time_spent', 'status'];
            
            for (const column of columns) {
                const hasColumn = await queryRunner.hasColumn("session_learner_action", column);
                if (hasColumn) {
                    await queryRunner.query(`ALTER TABLE "session_learner_action" DROP COLUMN "${column}"`);
                    console.log(`✅ Removed ${column} column from session_learner_action table`);
                }
            }

            // Make job_type NOT NULL again
            const hasJobTypeColumn = await queryRunner.hasColumn("session_learner_action", "job_type");
            if (hasJobTypeColumn) {
                await queryRunner.query(`
                    ALTER TABLE "session_learner_action" 
                    ALTER COLUMN "job_type" SET NOT NULL
                `);
                console.log('✅ Made job_type column NOT NULL in session_learner_action table');
            }

            // Add back learner_id column
            const hasLearnerIdColumn = await queryRunner.hasColumn("session_learner_action", "learner_id");
            if (!hasLearnerIdColumn) {
                await queryRunner.query(`
                    ALTER TABLE "session_learner_action" 
                    ADD "learner_id" integer
                `);
                
                await queryRunner.query(`
                    ALTER TABLE "session_learner_action" 
                    ADD CONSTRAINT "FK_session_learner_action_learner" 
                    FOREIGN KEY ("learner_id") REFERENCES "learner"("learner_id") ON DELETE CASCADE ON UPDATE NO ACTION
                `);
                console.log('✅ Added back learner_id column to session_learner_action table');
            }
        }
    }
}
