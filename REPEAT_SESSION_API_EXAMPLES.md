# Repeat Session API Usage Examples

## 1. Get Available Options for Dropdowns

```bash
GET http://localhost:4000/api/v1/session/repeat/options
Headers:
- Authorization: Bearer YOUR_TOKEN
```

**Response:**
```json
{
    "message": "Repeat session options fetched successfully",
    "status": true,
    "data": {
        "frequencies": ["Daily", "Weekly", "Monthly"],
        "file_types": ["ILP", "Review", "Assessment", "General"],
        "session_types": ["First Session", "Follow Up", "Review", "Assessment", "Final"]
    }
}
```

## 2. Create a Repeat Session (Weekly, Every 2 Weeks)

```bash
POST http://localhost:4000/api/v1/session/repeat/create
Headers:
- Authorization: Bearer YOUR_TOKEN
- Content-Type: application/json

Body:
{
    "original_session_id": 1,
    "frequency": "Weekly",
    "repeat_every": 2,
    "include_holidays": false,
    "include_weekends": false,
    "end_date": "2024-12-31",
    "upload_session_files": false
}
```

## 3. Create a Repeat Session with File Attachments

```bash
POST http://localhost:4000/api/v1/session/repeat/create
Headers:
- Authorization: Bearer YOUR_TOKEN
- Content-Type: application/json

Body:
{
    "original_session_id": 1,
    "frequency": "Monthly",
    "repeat_every": 1,
    "include_holidays": true,
    "include_weekends": true,
    "end_date": "2024-12-31",
    "upload_session_files": true,
    "file_attachments": [
        {
            "file_type": "ILP",
            "session_type": "First Session",
            "file_url": "/uploads/ilp_template.pdf",
            "file_name": "ILP_Template.pdf",
            "uploaded_at": "2024-01-15T10:00:00Z"
        }
    ]
}
```

## 4. Upload Session Files

```bash
POST http://localhost:4000/api/v1/session/repeat/upload-files
Headers:
- Authorization: Bearer YOUR_TOKEN
- Content-Type: multipart/form-data

Form Data:
- file: [FILE_TO_UPLOAD]
- repeat_session_id: 1
- file_type: "Assessment"
- session_type: "Review"
```

## 5. Get All Repeat Sessions

```bash
GET http://localhost:4000/api/v1/session/repeat/list
Headers:
- Authorization: Bearer YOUR_TOKEN
```

## 6. Get Repeat Sessions for Specific Trainer

```bash
GET http://localhost:4000/api/v1/session/repeat/list?trainer_id=5&is_active=true
Headers:
- Authorization: Bearer YOUR_TOKEN
```

## 7. Update Repeat Session Configuration

```bash
PATCH http://localhost:4000/api/v1/session/repeat/update/1
Headers:
- Authorization: Bearer YOUR_TOKEN
- Content-Type: application/json

Body:
{
    "frequency": "Monthly",
    "repeat_every": 1,
    "include_weekends": true,
    "end_date": "2025-06-30"
}
```

## 8. Cancel Repeat Session (Keep Existing Sessions)

```bash
PATCH http://localhost:4000/api/v1/session/repeat/cancel/1
Headers:
- Authorization: Bearer YOUR_TOKEN
- Content-Type: application/json

Body:
{
    "cancel_future_sessions": false
}
```

## 9. Cancel Repeat Session (Cancel Future Sessions)

```bash
PATCH http://localhost:4000/api/v1/session/repeat/cancel/1
Headers:
- Authorization: Bearer YOUR_TOKEN
- Content-Type: application/json

Body:
{
    "cancel_future_sessions": true
}
```

## Frontend Integration Notes

### Form Fields Mapping:
1. **Repeat frequency**: Maps to `frequency` field
2. **Repeat every (x) week(s)**: Maps to `repeat_every` field
3. **Include holidays?**: Maps to `include_holidays` field
4. **Include weekends?**: Maps to `include_weekends` field
5. **End date**: Maps to `end_date` field
6. **Upload session file attachment?**: Maps to `upload_session_files` field

### File Upload Flow:
1. User checks "Upload session file attachment?"
2. Show file upload button
3. When clicked, show modal with:
   - File type dropdown (populated from `/repeat/options`)
   - Session type dropdown (populated from `/repeat/options`)
   - File upload field
4. Submit to `/repeat/upload-files` endpoint

### Workflow:
1. User creates a session normally
2. If "Repeat session" checkbox is selected, show additional form
3. User fills repeat configuration
4. Submit to `/repeat/create` with the original session ID
5. System automatically generates future sessions
6. User can manage repeat sessions via list/update/cancel APIs

## Error Handling

All APIs return standard error responses:
```json
{
    "message": "Error description",
    "status": false,
    "error": "Detailed error message"
}
```

Common error codes:
- **400**: Bad request (missing required fields)
- **404**: Session/Repeat session not found
- **500**: Internal server error
