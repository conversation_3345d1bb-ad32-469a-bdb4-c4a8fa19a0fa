// import { createClient } from '@supabase/supabase-js';
// import { DataSource } from 'typeorm';

// const supabaseUrl = process.env.SUPABASE_URL
// const supabaseKey = process.env.SUPABASE_KEY

// const supabase = createClient(supabaseUrl, supabaseKey);

// export const AppDataSource = new DataSource({
//     type: 'postgres',
//     host: process.env.POSTGRES_HOST,
//     port: parseInt(process.env.POSTGRES_PORT as string),
//     username: process.env.POSTGRES_USERNAME,
//     password: process.env.POSTGRES_PASSWORD,
//     database: process.env.POSTGRES_DATABASE,
//     synchronize: true,
//     logging: false,
//     entities: [__dirname + '/../**/*.entity.{js,ts}'],
//     migrations: [],
//     subscribers: [],
//     extra: {
//         supabase,
//     },
// });

import { DataSource } from 'typeorm';

export const AppDataSource = new DataSource({
    type: 'postgres',
    host: process.env.POSTGRES_HOST,
    port: parseInt(process.env.POSTGRES_PORT as string, 10),
    username: process.env.POSTGRES_USERNAME,
    password: process.env.POSTGRES_PASSWORD,
    database: process.env.POSTGRES_DATABASE,
    synchronize: true,
    // dropSchema: true,
    logging: false,
    entities: [__dirname + '/../**/*.entity.ts'],
    migrations: [__dirname + '/migration/*.ts'],
    subscribers: [],
    extra: {
        ssl: {
            rejectUnauthorized: false,
        },
    },
});

I'm working on a backend (Node.js + Express) for a learning session management system. I need to implement the following:

1. **Create APIs** to manage session learner actions (as seen in the second screenshot). A trainer can assign tasks to learners for a session. Each action includes:
   - Action name
   - Action description
   - Target date
   - On/Off-the-job type
   - Who added the action (e.g., Assessor)
   - Optional unit (dropdown)
   - File upload (file to be displayed inline with the action row, not separately)

2. **In the session learner plan listing**, add a **filter for attended status** so the frontend can show only attended or non-attended learners per session.

3. **During session creation**, the user selects the number of participants. Based on that, the API fetches the list of learners for the selected trainer (this part is already implemented). Now:
   - When creating the session, allow selecting multiple learners.
   - Ensure the session is created and linked with all selected learners (currently learnerId is being passed as an array — check if this is working as expected, and fix if needed).

Also:
- Ensure validations (required fields) for the form in the second image.
- Add support for uploading and storing files per learner action (can be a local upload or S3 — keep logic ready).

Please generate Mongoose schemas or Sequelize models, controller and route functions, and upload logic (using `multer` if local). Let me know if more details are needed.
