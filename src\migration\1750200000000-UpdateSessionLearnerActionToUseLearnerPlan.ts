import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateSessionLearnerActionToUseLearnerPlan1750200000000 implements MigrationInterface {
    name = 'UpdateSessionLearnerActionToUseLearnerPlan1750200000000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Check if table exists
        const hasTable = await queryRunner.hasTable("session_learner_action");
        
        if (hasTable) {
            // Check if session_id column exists (old structure)
            const hasSessionIdColumn = await queryRunner.hasColumn("session_learner_action", "session_id");
            const hasLearnerPlanIdColumn = await queryRunner.hasColumn("session_learner_action", "learner_plan_id");
            
            if (hasSessionIdColumn && !hasLearnerPlanIdColumn) {
                // Drop old foreign key constraint
                try {
                    await queryRunner.query(`
                        ALTER TABLE "session_learner_action" 
                        DROP CONSTRAINT IF EXISTS "FK_session_learner_action_session"
                    `);
                } catch (error) {
                    console.log('ℹ️ Old foreign key constraint not found or already dropped');
                }
                
                // Add new learner_plan_id column
                await queryRunner.query(`
                    ALTER TABLE "session_learner_action" 
                    ADD COLUMN "learner_plan_id" integer
                `);
                
                // Add new foreign key constraint
                await queryRunner.query(`
                    ALTER TABLE "session_learner_action" 
                    ADD CONSTRAINT "FK_session_learner_action_learner_plan" 
                    FOREIGN KEY ("learner_plan_id") REFERENCES "learner_plan"("learner_plan_id") ON DELETE CASCADE ON UPDATE NO ACTION
                `);
                
                // Drop old session_id column
                await queryRunner.query(`
                    ALTER TABLE "session_learner_action" 
                    DROP COLUMN "session_id"
                `);
                
                console.log('✅ Updated session_learner_action table to use learner_plan_id instead of session_id');
            } else if (hasLearnerPlanIdColumn) {
                console.log('ℹ️ session_learner_action table already uses learner_plan_id');
            } else {
                console.log('ℹ️ session_learner_action table structure is unexpected');
            }
        } else {
            console.log('ℹ️ session_learner_action table does not exist');
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Check if table exists
        const hasTable = await queryRunner.hasTable("session_learner_action");
        
        if (hasTable) {
            // Check if learner_plan_id column exists (new structure)
            const hasLearnerPlanIdColumn = await queryRunner.hasColumn("session_learner_action", "learner_plan_id");
            const hasSessionIdColumn = await queryRunner.hasColumn("session_learner_action", "session_id");
            
            if (hasLearnerPlanIdColumn && !hasSessionIdColumn) {
                // Drop new foreign key constraint
                try {
                    await queryRunner.query(`
                        ALTER TABLE "session_learner_action" 
                        DROP CONSTRAINT IF EXISTS "FK_session_learner_action_learner_plan"
                    `);
                } catch (error) {
                    console.log('ℹ️ New foreign key constraint not found or already dropped');
                }
                
                // Add old session_id column
                await queryRunner.query(`
                    ALTER TABLE "session_learner_action" 
                    ADD COLUMN "session_id" integer
                `);
                
                // Add old foreign key constraint
                await queryRunner.query(`
                    ALTER TABLE "session_learner_action" 
                    ADD CONSTRAINT "FK_session_learner_action_session" 
                    FOREIGN KEY ("session_id") REFERENCES "session"("session_id") ON DELETE CASCADE ON UPDATE NO ACTION
                `);
                
                // Drop new learner_plan_id column
                await queryRunner.query(`
                    ALTER TABLE "session_learner_action" 
                    DROP COLUMN "learner_plan_id"
                `);
                
                console.log('✅ Reverted session_learner_action table to use session_id instead of learner_plan_id');
            } else {
                console.log('ℹ️ session_learner_action table structure cannot be reverted');
            }
        }
    }
}
