import { Response } from 'express';
import { CustomRequest } from '../util/Interface/expressInterface';
import { AppDataSource } from '../data-source';
import { SessionLearnerAction, JobType } from '../entity/SessionLearnerAction.entity';
import { Session } from '../entity/Session.entity';
import { Learner } from '../entity/Learner.entity';
import { User } from '../entity/User.entity';
import { Course } from '../entity/Course.entity';
import { uploadToS3 } from '../util/aws';

export class SessionLearnerActionController {

    public async createAction(req: CustomRequest, res: Response) {
        try {
            const actionRepository = AppDataSource.getRepository(SessionLearnerAction);
            const sessionRepository = AppDataSource.getRepository(Session);
            const learnerRepository = AppDataSource.getRepository(Learner);
            const userRepository = AppDataSource.getRepository(User);

            const {
                session_id,
                learner_id,
                action_name,
                action_description,
                target_date,
                job_type,
                unit
            } = req.body;

            // Validate required fields
            if (!session_id || !learner_id || !action_name || !target_date) {
                return res.status(400).json({
                    message: "Session ID, Learner ID, Action Name, and Target Date are required",
                    status: false
                });
            }

            // Verify session exists
            const session = await sessionRepository.findOne({ where: { session_id } });
            if (!session) {
                return res.status(404).json({
                    message: "Session not found",
                    status: false
                });
            }

            // Verify learner exists
            const learner = await learnerRepository.findOne({ where: { learner_id } });
            if (!learner) {
                return res.status(404).json({
                    message: "Learner not found",
                    status: false
                });
            }

            // Get the user who is adding the action
            const addedBy = await userRepository.findOne({ where: { user_id: req.user.user_id } });

            let fileAttachment = null;
            if (req.file) {
                // Upload file to S3
                const s3Upload = await uploadToS3(req.file, "SessionActions");
                fileAttachment = {
                    file_name: req.file.originalname,
                    file_size: req.file.size,
                    file_url: s3Upload.url,
                    s3_key: s3Upload.key,
                    uploaded_at: new Date()
                };
            }

            // Create action
            const action = actionRepository.create({
                session,
                learner,
                action_name,
                action_description,
                target_date: new Date(target_date),
                job_type: job_type || JobType.OnTheJob,
                added_by: addedBy,
                unit: unit || null,
                file_attachment: fileAttachment
            });

            const savedAction = await actionRepository.save(action);

            // Fetch action with relations for response
            const actionWithRelations = await actionRepository.findOne({
                where: { action_id: savedAction.action_id },
                relations: ['session', 'learner', 'learner.user_id', 'added_by']
            });

            return res.status(201).json({
                message: "Session learner action created successfully",
                status: true,
                data: actionWithRelations
            });

        } catch (error) {
            return res.status(500).json({
                message: "Internal Server Error",
                status: false,
                error: error.message
            });
        }
    }

    public async getActionsBySession(req: CustomRequest, res: Response) {
        try {
            const actionRepository = AppDataSource.getRepository(SessionLearnerAction);
            const { session_id } = req.params;
            const { learner_id } = req.query;

            let queryBuilder = actionRepository.createQueryBuilder('action')
                .leftJoinAndSelect('action.session', 'session')
                .leftJoinAndSelect('action.learner', 'learner')
                .leftJoinAndSelect('learner.user_id', 'learner_user')
                .leftJoinAndSelect('action.added_by', 'added_by')
                .where('session.session_id = :session_id', { session_id });

            if (learner_id) {
                queryBuilder.andWhere('learner.learner_id = :learner_id', { learner_id });
            }

            const actions = await queryBuilder
                .orderBy('action.created_at', 'DESC')
                .getMany();

            return res.status(200).json({
                message: "Session learner actions fetched successfully",
                status: true,
                data: actions
            });

        } catch (error) {
            return res.status(500).json({
                message: "Internal Server Error",
                status: false,
                error: error.message
            });
        }
    }

    public async updateAction(req: CustomRequest, res: Response) {
        try {
            const actionRepository = AppDataSource.getRepository(SessionLearnerAction);
            const { id } = req.params;
            const {
                action_name,
                action_description,
                target_date,
                job_type,
                unit
            } = req.body;

            const action = await actionRepository.findOne({ where: { action_id: parseInt(id) } });
            if (!action) {
                return res.status(404).json({
                    message: "Action not found",
                    status: false
                });
            }

            // Handle file upload if provided
            let fileAttachment = action.file_attachment;
            if (req.file) {
                const s3Upload = await uploadToS3(req.file, "SessionActions");
                fileAttachment = {
                    file_name: req.file.originalname,
                    file_size: req.file.size,
                    file_url: s3Upload.url,
                    s3_key: s3Upload.key,
                    uploaded_at: new Date()
                };
            }

            // Update action
            action.action_name = action_name || action.action_name;
            action.action_description = action_description || action.action_description;
            action.target_date = target_date ? new Date(target_date) : action.target_date;
            action.job_type = job_type || action.job_type;
            action.unit = unit !== undefined ? unit : action.unit;
            action.file_attachment = fileAttachment;

            const updatedAction = await actionRepository.save(action);

            // Fetch with relations
            const actionWithRelations = await actionRepository.findOne({
                where: { action_id: updatedAction.action_id },
                relations: ['session', 'learner', 'learner.user_id', 'added_by']
            });

            return res.status(200).json({
                message: "Action updated successfully",
                status: true,
                data: actionWithRelations
            });

        } catch (error) {
            return res.status(500).json({
                message: "Internal Server Error",
                status: false,
                error: error.message
            });
        }
    }

    public async deleteAction(req: CustomRequest, res: Response) {
        try {
            const actionRepository = AppDataSource.getRepository(SessionLearnerAction);
            const { id } = req.params;

            const action = await actionRepository.findOne({ where: { action_id: parseInt(id) } });
            if (!action) {
                return res.status(404).json({
                    message: "Action not found",
                    status: false
                });
            }

            await actionRepository.remove(action);

            return res.status(200).json({
                message: "Action deleted successfully",
                status: true
            });

        } catch (error) {
            return res.status(500).json({
                message: "Internal Server Error",
                status: false,
                error: error.message
            });
        }
    }

    public async getActionOptions(req: CustomRequest, res: Response) {
        try {
            const courseRepository = AppDataSource.getRepository(Course);
            
            // Get all courses with units for dropdown
            const courses = await courseRepository.find({
                select: ['course_id', 'course_name', 'units']
            });

            // Extract units from all courses
            const allUnits = [];
            courses.forEach(course => {
                if (course.units && Array.isArray(course.units)) {
                    course.units.forEach((unit: any) => {
                        allUnits.push({
                            unit_id: unit.id || unit.unit_id,
                            unit_name: unit.title || unit.name,
                            unit_ref: unit.unit_ref || unit.component_ref || unit.section_ref,
                            course_name: course.course_name
                        });
                    });
                }
            });

            const options = {
                job_types: Object.values(JobType),
                units: allUnits
            };

            return res.status(200).json({
                message: "Action options fetched successfully",
                status: true,
                data: options
            });

        } catch (error) {
            return res.status(500).json({
                message: "Internal Server Error",
                status: false,
                error: error.message
            });
        }
    }
}
