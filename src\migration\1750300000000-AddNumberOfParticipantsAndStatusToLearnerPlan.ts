import { MigrationInterface, QueryRunner } from "typeorm";

export class AddNumberOfParticipantsAndStatusToLearnerPlan1750300000000 implements MigrationInterface {
    name = 'AddNumberOfParticipantsAndStatusToLearnerPlan1750300000000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Check if numberOfParticipants column exists
        const hasNumberOfParticipantsColumn = await queryRunner.hasColumn("learner_plan", "numberOfParticipants");
        
        if (!hasNumberOfParticipantsColumn) {
            await queryRunner.query(`
                ALTER TABLE "learner_plan" 
                ADD "numberOfParticipants" integer DEFAULT 0
            `);
            console.log('✅ Added numberOfParticipants column to learner_plan table');
        } else {
            console.log('ℹ️ numberOfParticipants column already exists in learner_plan table');
        }

        // Check if status column exists
        const hasStatusColumn = await queryRunner.hasColumn("learner_plan", "status");
        
        if (!hasStatusColumn) {
            await queryRunner.query(`
                ALTER TABLE "learner_plan" 
                ADD "status" boolean DEFAULT false
            `);
            console.log('✅ Added status column to learner_plan table');
        } else {
            console.log('ℹ️ status column already exists in learner_plan table');
        }
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Remove numberOfParticipants column
        const hasNumberOfParticipantsColumn = await queryRunner.hasColumn("learner_plan", "numberOfParticipants");
        
        if (hasNumberOfParticipantsColumn) {
            await queryRunner.query(`ALTER TABLE "learner_plan" DROP COLUMN "numberOfParticipants"`);
            console.log('✅ Removed numberOfParticipants column from learner_plan table');
        }

        // Remove status column
        const hasStatusColumn = await queryRunner.hasColumn("learner_plan", "status");
        
        if (hasStatusColumn) {
            await queryRunner.query(`ALTER TABLE "learner_plan" DROP COLUMN "status"`);
            console.log('✅ Removed status column from learner_plan table');
        }
    }
}
