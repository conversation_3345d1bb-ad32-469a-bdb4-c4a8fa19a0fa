# Repeat Session API Documentation

## Overview
The Repeat Session functionality allows users to create recurring sessions with configurable scheduling options and file attachments.

## API Endpoints

### 1. **Create Repeat Session**
**POST** `/api/v1/session/repeat/create`

Creates a repeat session configuration and generates future sessions automatically.

#### Request Body:
```json
{
    "original_session_id": 1,
    "frequency": "Weekly",
    "repeat_every": 2,
    "include_holidays": false,
    "include_weekends": false,
    "end_date": "2024-12-31",
    "upload_session_files": true,
    "file_attachments": [
        {
            "file_type": "ILP",
            "session_type": "First Session",
            "file_url": "/uploads/session_file.pdf",
            "file_name": "session_plan.pdf",
            "uploaded_at": "2024-01-15T10:00:00Z"
        }
    ]
}
```

#### Response:
```json
{
    "message": "Repeat session created successfully",
    "status": true,
    "data": {
        "repeat_session": {
            "repeat_session_id": 1,
            "frequency": "Weekly",
            "repeat_every": 2,
            "include_holidays": false,
            "include_weekends": false,
            "end_date": "2024-12-31",
            "generated_sessions": [...]
        },
        "generated_sessions": [...]
    }
}
```

### 2. **Get Repeat Sessions**
**GET** `/api/v1/session/repeat/list`

Retrieves all repeat session configurations.

#### Query Parameters:
- `trainer_id` (optional): Filter by trainer ID
- `is_active` (optional): Filter by active status (true/false)

#### Response:
```json
{
    "message": "Repeat sessions fetched successfully",
    "status": true,
    "data": [
        {
            "repeat_session_id": 1,
            "frequency": "Weekly",
            "repeat_every": 2,
            "include_holidays": false,
            "include_weekends": false,
            "end_date": "2024-12-31",
            "is_active": true,
            "original_session": {...},
            "file_attachments": [...]
        }
    ]
}
```

### 3. **Update Repeat Session**
**PATCH** `/api/v1/session/repeat/update/:id`

Updates an existing repeat session configuration.

#### Request Body:
```json
{
    "frequency": "Monthly",
    "repeat_every": 1,
    "include_weekends": true,
    "end_date": "2025-01-31"
}
```

### 4. **Cancel Repeat Session**
**PATCH** `/api/v1/session/repeat/cancel/:id`

Cancels a repeat session and optionally cancels future sessions.

#### Request Body:
```json
{
    "cancel_future_sessions": true
}
```

### 5. **Upload Session Files**
**POST** `/api/v1/session/repeat/upload-files`

Uploads files for a repeat session configuration.

#### Form Data:
- `file`: File to upload
- `repeat_session_id`: ID of the repeat session
- `file_type`: Type of file (ILP, Review, Assessment, General)
- `session_type`: Session type (First Session, Follow Up, Review, Assessment, Final)

### 6. **Get Repeat Session Options**
**GET** `/api/v1/session/repeat/options`

Returns available options for dropdowns.

#### Response:
```json
{
    "message": "Repeat session options fetched successfully",
    "status": true,
    "data": {
        "frequencies": ["Daily", "Weekly", "Monthly"],
        "file_types": ["ILP", "Review", "Assessment", "General"],
        "session_types": ["First Session", "Follow Up", "Review", "Assessment", "Final"]
    }
}
```

## Data Models

### RepeatSession Entity
```typescript
{
    repeat_session_id: number;
    original_session: Session;
    frequency: 'Daily' | 'Weekly' | 'Monthly';
    repeat_every: number;
    include_holidays: boolean;
    include_weekends: boolean;
    end_date: Date;
    upload_session_files: boolean;
    file_attachments: FileAttachment[];
    generated_sessions: GeneratedSession[];
    is_active: boolean;
    created_at: Date;
    updated_at: Date;
}
```

### FileAttachment Structure
```typescript
{
    file_type: 'ILP' | 'Review' | 'Assessment' | 'General';
    session_type: 'First Session' | 'Follow Up' | 'Review' | 'Assessment' | 'Final';
    file_url: string;
    file_name: string;
    uploaded_at: Date;
}
```

### GeneratedSession Structure
```typescript
{
    session_id: number;
    scheduled_date: Date;
    status: 'pending' | 'created' | 'cancelled';
}
```

## Features

✅ **Configurable Frequency**: Daily, Weekly, Monthly options
✅ **Custom Intervals**: Repeat every X weeks/months
✅ **Holiday/Weekend Control**: Include or exclude holidays and weekends
✅ **End Date Management**: Set when repeat sessions should stop
✅ **File Attachments**: Upload and categorize session files
✅ **Automatic Generation**: Creates future sessions automatically
✅ **Notification System**: Sends notifications for each generated session
✅ **Cancellation Support**: Cancel repeat configuration and future sessions
✅ **Status Tracking**: Track status of generated sessions
